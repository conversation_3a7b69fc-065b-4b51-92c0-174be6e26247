import { test, expect, slowExpect, customExpect } from '../fixtures/ui-fixtures'
import { StringUtils, PlaywrightUtils } from '../../commons/common-functions'
import { EnvUtils } from '../../commons/env-utilities'

test.describe(
  'LMM UI Tests',
  {
    tag: ['@lmm', '@ui'],
  },
  () => {
    const currentEnv = EnvUtils.getInstance()
    test('Verify user is able to upload center taxonamy', async ({ adminPage }) => {
      const centerName = process.env.CENTER_NAME ? process.env.CENTER_NAME : ''
      const streamName = 'JEE Advanced'
      const className = 'Class 11'
      const sessionValue = process.env.CENTER_SESSION ? process.env.CENTER_SESSION : ''
      const uploadInputFilePath = 'test-data/topology-upload-template-sheet.csv'

      /**
       * @info Test Naming convention updated and assertion message
       */
      //navigate to taxonamy
      await test.step(`Navigate to taxonamy management page`, async () => {
        await expect(adminPage.pos.teacherHomePage.page).toHaveURL(adminPage.pos.teacherHomePage.url)
        await adminPage.goto(adminPage.pos.taxonamyPage.url) //this end point for automation purpose
      })

      //Verify upload center taxonamy
      await test.step(`Verify upload center taxonamy`, async () => {
        await adminPage.pos.taxonamyPage.verifyUploadTaxonamyToCenterPage()
        await adminPage.pos.taxonamyPage.uploadTaxonamyToCenterDetails(centerName, streamName, className, sessionValue)
        await adminPage.pos.taxonamyPage.verifyUploadTaxonamyToCenter(uploadInputFilePath)
      })
    })

    test('Verify user is able to view & edit upload center taxonamy', async ({ adminPage }) => {
      const subjectName = 'Chemistry'
      const centerName = process.env.CENTER_NAME ? process.env.CENTER_NAME : ''
      const streamName = 'JEE Advanced'
      const className = 'Class 11'
      const sessionValue = process.env.CENTER_SESSION ? process.env.CENTER_SESSION : ''

      //navigate to taxonamy
      await test.step(`Navigate to taxonamy management page`, async () => {
        await expect(adminPage.pos.teacherHomePage.page).toHaveURL(adminPage.pos.teacherHomePage.url)
        await adminPage.goto(adminPage.pos.taxonamyPage.url) //this end point for automation purpose
        // await expect(adminPage).toHaveURL(/.*e2e-automation/);
      })

      //view & edit center taxonamy
      await test.step(`Verify view & edit center taxonamy`, async () => {
        await adminPage.pos.taxonamyPage.clickOnViewAndEditTaxonamy()
        await adminPage.pos.taxonamyPage.uploadTaxonamyToCenterDetails(centerName, streamName, className, sessionValue)
        await adminPage.pos.taxonamyPage.verifyEditTaxonamy(subjectName)
      })
    })

    if (currentEnv.isStage()) {
      // test('Verify user is able upload non academic content', async ({ adminPage }) => {
      //   const uploadInputFilePath = 'test-data/topology-upload-template-sheet.csv'
      //   const description = 'ContentDescription'
      //   const contentName = 'ContentName' + StringUtils.generateRandomString(3)

      //   //navigate to taxonamy
      //   await test.step(`Navigate to taxonamy management page`, async () => {
      //     await expect(adminPage.pos.teacherHomePage.page).toHaveURL(adminPage.pos.teacherHomePage.url)
      //     await adminPage.goto(adminPage.pos.nonAcademicContentPage.url) //
      //     await adminPage.waitForLoadState('networkidle')
      //   })

      //   //view & edit center taxonamy
      //   await test.step(`Verify non academic content uploaded`, async () => {
      //     await adminPage.pos.nonAcademicContentPage.uploadNewContent(uploadInputFilePath)
      //     await adminPage.pos.nonAcademicContentPage.completeMetaDataUpload(description, contentName)
      //   })
      // })
    }

    test('Verify user is able to borrow taxonomy', async ({ adminPage }) => {
      const centerName = process.env.CENTER_NAME ? process.env.CENTER_NAME : ''
      const streamName = 'JEE Advanced'
      const sessionValue = process.env.CENTER_SESSION ? process.env.CENTER_SESSION : ''
      const centerNameTo = 'KOTA'
      const streamNameTo = 'JEE Advanced'

      //navigate to taxonamy
      await test.step(`Navigate to taxonamy management page`, async () => {
        await expect(adminPage.pos.teacherHomePage.page).toHaveURL(adminPage.pos.teacherHomePage.url)
        await adminPage.goto(adminPage.pos.taxonamyPage.url) //this end point for automation purpose
      })

      //verify borrow taxonomy from one center to another center
      await test.step(`verify borrow taxonomy from one center to another center`, async () => {
        await adminPage.goto(adminPage.pos.taxonamyPage.url)
        await adminPage.pos.taxonamyPage.selectFromBorrowTaxonamyDetails(centerName, streamName, sessionValue)
        await adminPage.pos.taxonamyPage.selectToBorrowTaxonamyDetails(centerNameTo, streamNameTo, sessionValue)
        await adminPage.pos.taxonamyPage.verifyBorrowTaxonamyToCenter()
      })
    })

    test('Verify content attach to library by uploading csv', async ({ adminPage, lmmTest }) => {
      test.setTimeout(80000)
      const centerName = process.env.CENTER_NAME ? process.env.CENTER_NAME : ''
      const courseName_Lmm = process.env.COURSE_NAME ? process.env.COURSE_NAME : ''
      const baseURL = process.env.BASE_URL ? process.env.BASE_URL : ''
      let contentPdf = 'test-data/uploadfile.pdf'
      let pdfContentCSVFileProd = 'test-data/single_pdf_content_prod.csv'
      let pdfContentCSVFile = 'test-data/single_pdf_content.csv'
      let contentNewName = 'Automation_' + StringUtils.generateRandomString(5)
      let subjectName = 'Chemistry'
      let superTopicName = 'Physical Chemistry'
      const timezone = await PlaywrightUtils.getBrowserTimezone(adminPage)
      const currentDate = StringUtils.getCurrentTimeWithOffset('YYYY-MM-DD', 0, false, timezone)
      const phaseNumber = '1'
      const topicName = 'Chemical Equilibrium'
      const firstTopicName = 'Atomic Structure'
      const replaceFile = 'test-data/SampleFile.pdf'
      const type = 'STUDY_MATERIALS'
      const sub_type = 'STUDY_MODULE'
      let contentId = ''

      await test.step(`Navigate from teacher home page to internal user home page`, async () => {
        await expect(adminPage).toHaveURL(adminPage.pos.teacherHomePage.url)
        await adminPage.pos.teacherHomePage.navigateToInternalUser()
        await adminPage.waitForLoadState('networkidle')
        await slowExpect(adminPage).toHaveURL(/.*internal-user/)
      })

      await test.step(`Navigate to lmm page`, async () => {
        await expect(
          adminPage.pos.internalUserHomePage.contentManagementButton,
          'verify content management button is visible',
        ).toBeVisible()
        await adminPage.pos.internalUserHomePage.contentManagementButton.click()
        await expect(adminPage.pos.internalUserHomePage.lmmButton, 'verify LMM  button is visible').toBeVisible()
        await adminPage.pos.internalUserHomePage.lmmButton.click()
        await slowExpect(adminPage).toHaveURL(/.*lmm/)
      })

      await test.step(` Verify the user can able to navigate to different tabs`, async () => {
        await adminPage.pos.lmmPage.validateTabNaviagtions()
      })

      await test.step(`Verify upload material content to library via csv file`, async () => {
        if (currentEnv.isProd()) {
          await adminPage.pos.lmmPage.uploadNewContent(contentPdf, pdfContentCSVFileProd, contentNewName)
        } else {
          await adminPage.pos.lmmPage.uploadNewContent(contentPdf, pdfContentCSVFile, contentNewName)
        }
      })

      await test.step(`Verify content name uploaded via csv file`, async () => {
        await adminPage.pos.lmmPage.validateUploadedContentName(contentNewName)
        contentId = await adminPage.pos.lmmPage.validateSearchWithContentId(contentNewName)
      })

      await test.step(`Set Test Data For Cleaning Up Uploaded LMM Content `, async () => {
        lmmTest.lmmContent.push({
          id: contentId,
          name: contentNewName,
          type: type,
          subType: sub_type,
        })
        console.log(`Value updated: id=${lmmTest.lmmContent}`)
      })

      await test.step(`Verify the user can able to Replace the Material and Incompeted contents`, async () => {
        await adminPage.pos.lmmPage.validateReplaceFile(replaceFile)
      })

      await test.step(`Verify the user can able to Preview the contents`, async () => {
        await adminPage.pos.lmmPage.validatePreviewUploadedContent(contentNewName)
      })

      await test.step(`Navigate to course management page and apply filter for first topology`, async () => {
        await adminPage.pos.internalUserHomePage.navigateToCourseAndSyllabusPage()
        await adminPage.waitForLoadState('networkidle')
        await expect(adminPage).toHaveURL(/.*course-syllabus*/)
        await adminPage.pos.courseAndSyllabusPage.applyFilterToAutomationCenter(centerName, courseName_Lmm)
      })

      await test.step(`Navigate to attach content page and apply filter`, async () => {
        await adminPage.pos.courseAndSyllabusPage.navigateToAttachContentPage()
        await adminPage.pos.courseAndSyllabusPage.filterUploadedContent(topicName, baseURL)
      })

      await test.step(`Verify uploaded content from course page`, async () => {
        await adminPage.pos.courseAndSyllabusPage.verifyContentName(contentNewName)
      })

      await test.step(`Verify assigned the uploaded subject level material content to coursee`, async () => {
        await adminPage.pos.courseAndSyllabusPage.assignTheUploadedContentToCourse(
          contentNewName,
          phaseNumber,
          currentDate,
        )
        await adminPage.pos.courseAndSyllabusPage.verifyUploadSuccessful()
      })

      /**
       * @info Commenting Student side validation, due to content attach is not visible to Student
       */

      // await test.step(`Verify login to student side`, async () => {
      //   await studentPage.login();
      //   await expect(studentPage.pos.homePage.isLoggedIn()).toBeTruthy();
      // });

      // await test.step(`Validating supertopics`, async () => {
      //   await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText, 'verifying "explore Study Materials" text should be visible').toBeVisible();
      //   await studentPage.pos.subjectDetailsPage.checkAndClickSubjectUnderContinueLearning(subjectName!);
      //   //await studentPage.waitForLoadState('networkidle');
      //   // await studentPage.waitForLoadState('domcontentloaded');
      // });

      // await test.step(`Verify the assigned content from student side`, async () => {
      //   await studentPage.pos.subjectDetailsPage.validateAssignedContentInSubject(subjectName, superTopicName, topicName, contentNewName, firstTopicName);
      // });

      // await test.step(`Verify the user can able to open the materials `, async () => {
      //   await studentPage.pos.subjectDetailsPage.validateOpenUploadedContent(contentNewName);
      //   await expect(studentPage).toHaveURL(/.*pdf_viewer.html/);
      // });
    })

    test('Verify subject level content upload from meta data page', async ({ adminPage, lmmTest }) => {
      test.setTimeout(80000)
      const centerName = process.env.CENTER_NAME ? process.env.CENTER_NAME : ''
      const baseURL = process.env.BASE_URL ? process.env.BASE_URL : ''
      const courseName_Lmm = process.env.COURSE_NAME ? process.env.COURSE_NAME : ''

      // const courseName_Lmm = "Automation Testing";
      let contentPdf = 'test-data/uploadfile.pdf'
      let contentNameSubjectLevel = 'Automation_' + StringUtils.generateRandomString(5)
      let subjectName = 'Chemistry'
      const superTopicName = 'Physical Chemistry'
      const topicName = 'Chemical Equilibrium'
      const firstTopicName = 'Atomic Structure'
      const timezone = await PlaywrightUtils.getBrowserTimezone(adminPage)
      const currentDate = StringUtils.getCurrentTimeWithOffset('YYYY-MM-DD', 0, false, timezone)
      const phaseNumber = '1'
      const uploadType = 'SubjectLevel'

      const subjectLevelTopicName = 'None'
      const taxonamyName = 'ADPL_STREAM_JEE_MAIN_ADVANCED_2024_2025'

      const typeName = 'Revision Material'
      const subTypeName = 'Handbook'
      const learningCategoryNameName = 'Instructed'
      const sessionName = '04/2024 - 03/2025'
      const className = 'Class 11'
      const languageName = 'ENGLISH'
      const contentName = 'Testing'
      const type = 'REVISION_MATERIAL'
      const sub_type = 'HANDBOOK'
      let contentId = ''

      await test.step(`Navigate from teacher home page to internal user home page`, async () => {
        await expect(adminPage).toHaveURL(adminPage.pos.teacherHomePage.url)
        await adminPage.pos.teacherHomePage.navigateToInternalUser()
        await adminPage.waitForLoadState('networkidle')
        await slowExpect(adminPage).toHaveURL(/.*internal-user/)
      })

      await test.step(`Navigate to lmm page`, async () => {
        await expect(
          adminPage.pos.internalUserHomePage.contentManagementButton,
          'Verify content management button is visible',
        ).toBeVisible()
        await adminPage.pos.internalUserHomePage.contentManagementButton.click()
        await expect(adminPage.pos.internalUserHomePage.lmmButton, 'Verify LMM  button is visible').toBeVisible()
        await adminPage.pos.internalUserHomePage.lmmButton.click()
        await slowExpect(adminPage).toHaveURL(/.*lmm/)
      })

      await test.step(`Upload content material from meta data page in console`, async () => {
        await adminPage.pos.lmmPage.navigateToUploadContent(contentPdf)
        await adminPage.pos.lmmPage.enterDetailsOnMetaDataPage(
          uploadType,
          contentNameSubjectLevel,
          typeName,
          subTypeName,
          learningCategoryNameName,
          sessionName,
          centerName,
          taxonamyName,
          className,
          subjectName,
          superTopicName,
          topicName,
          languageName,
        )
      })

      await test.step(`Verify click on upload content`, async () => {
        await adminPage.pos.lmmPage.validateUploadFromFillFromConsole()
      })

      await test.step(`Verify content name uploaded - subject level from meta data page`, async () => {
        await adminPage.pos.lmmPage.validateUploadedContentName(contentNameSubjectLevel)
        contentId = await adminPage.pos.lmmPage.validateSearchWithContentId(contentNameSubjectLevel)
      })

      await test.step(`Set Test Data For Cleaning Up Uploaded LMM Content `, async () => {
        lmmTest.lmmContent.push({
          id: contentId,
          name: contentNameSubjectLevel,
          type: type,
          subType: sub_type,
        })
        console.log(`Value updated: id=${lmmTest.lmmContent}`)
      })

      await test.step('. Verify search the content name with Sort  ', async () => {
        await adminPage.pos.lmmPage.validateSearchWithSort(contentName)
      })

      await test.step(`Navigate to course management page and apply filter for first topology`, async () => {
        await adminPage.pos.internalUserHomePage.navigateToCourseAndSyllabusPage()
        await expect(adminPage).toHaveURL(/.*course-syllabus*/)
        await customExpect(15000)(adminPage.pos.courseAndSyllabusPage.apiLoader, "Verify page loading").not.toBeVisible();
        await adminPage.pos.courseAndSyllabusPage.applyFilterToAutomationCenter(centerName, courseName_Lmm)
      })

      await test.step(`Navigate to attach content page and apply filter`, async () => {
        await adminPage.pos.courseAndSyllabusPage.navigateToAttachContentPage()
        await adminPage.pos.courseAndSyllabusPage.filterUploadedContent(subjectLevelTopicName, baseURL)
      })

      await test.step(`Verify uploaded content from course page`, async () => {
        await adminPage.pos.courseAndSyllabusPage.verifyContentName(contentNameSubjectLevel)
      })

      await test.step(`Verify assigned the uploaded subject level material content to coursee`, async () => {
        await adminPage.pos.courseAndSyllabusPage.assignTheUploadedContentToCourse(
          contentNameSubjectLevel,
          phaseNumber,
          currentDate,
        )
        await adminPage.pos.courseAndSyllabusPage.verifyUploadSuccessful()
      })

      /**
       * @info Commenting Student side validation, due to content attach is not visible to Student
       */

      // await test.step(`Verify login to student side`, async () => {
      //   await studentPage.login();
      //   await expect(studentPage.pos.homePage.isLoggedIn()).toBeTruthy();
      // });

      // await test.step(`Validating supertopics`, async () => {
      //   await slowExpect(studentPage.pos.homePage.exploreStudyMaterialsText, 'verifying "explore Study Materials" text should be visible').toBeVisible();
      //   await studentPage.pos.subjectDetailsPage.checkAndClickSubjectUnderContinueLearning(subjectName!);
      //   // await studentPage.waitForLoadState('domcontentloaded');
      // });

      // await test.step(`Verify the assigned subject level content from student side`, async () => {
      //   await studentPage.pos.subjectDetailsPage.validateAssignedContentInSubjectLevel(subjectName, topicName, subTypeName, firstTopicName);
      // });

      // await test.step(`Verify the user can able to open the Subject level contents `, async () => {
      //   await studentPage.pos.subjectDetailsPage.validateOpenSubjectLevelContent(subTypeName);
      //   await expect(studentPage).toHaveURL(/.*pdf_viewer.html/);
      // });
    });


    test('Verify multitopology', async ({ adminPage, lmmTest }) => {
      test.setTimeout(80000);
      // Constants
      const courseName_Lmm = process.env.COURSE_NAME ? process.env.COURSE_NAME : ''
      const centerName = process.env.CENTER_NAME ? process.env.CENTER_NAME : ''
      const contentPdf = 'test-data/uploadfile.pdf'
      const contentNewName = `MultiTopologyAutomation${StringUtils.generateRandomString(5)} `
      const type = 'STUDY_MATERIAL'
      const sub_type = 'SPECIAL_BOOKLET'
      let contentId = ''
      let courseName = process.env.COURSE_NAME ? process.env.COURSE_NAME : ''
      // Metadata Page Data
      let metaData =
      {
        topicName: 'Chemical Equilibrium',
        superTopicName: 'Physical Chemistry',
        taxonomyName: 'ADPL_STREAM_JEE_MAIN_ADVANCED_2024_2025',
        typeName: 'Study Material',
        subTypeName: 'Special Booklet',
        learningCategoryName: 'Instructed',
        sessionName: '04/2024 - 03/2025',
        className: 'Class 11',
        languageName: 'ENGLISH',
        subjectName: 'Chemistry',
      }



      let metaDataProd = {
        topicName: 'Wave Optics',
        subjectName: 'Physics',
        taxonomyName: 'ADPL_STREAM_JEE_MAIN_ADVANCED_2024_2025',
        superTopicName: 'Optics',
        className: 'Class 12 Pass',
        classNameFilter: 'Class 12 Plus',
        center: 'ADPL',
        streamName: 'JEE Advanced',
        languageFilter: 'English',
        subjectNameFilter: 'Physics ( NEET, Class 12 Plus )',
      }

      // MultiTopology Data
      const multiTopologyData = [
        {
          topicName: 'Wave Optics',
          subjectName: 'Physics',
          taxonomyName: 'ADPL_STREAM_PRE_MEDICAL_2024_2025',
          superTopicName: 'Optics',
          className: 'Class 12 Pass',
          classNameFilter: 'Class 12 Plus',
          center: 'ADPL',
          streamName: 'NEET',
          languageFilter: 'English',
          subjectNameFilter: 'Physics ( NEET, Class 12 Plus )',
        },
        {
          topicName: 'Polynomials',
          subjectName: 'Maths',
          taxonomyName: 'KOTA_STREAM_PNCF_NCERT_2024_2025',
          superTopicName: 'Maths',
          className: 'Class 10',
          classNameFilter: 'Class 10',
          center: 'KOTA',
          streamName: 'NCERT',
          languageFilter: 'English',
          subjectNameFilter: 'Maths ( NCERT, Class 10 )',
        },
        {
          topicName: 'Chemical Equilibrium',
          subjectName: 'Chemistry',
          taxonomyName: 'ADPL_STREAM_JEE_MAIN_ADVANCED_2024_2025',
          superTopicName: 'Optics',
          className: 'Class 11',
          classNameFilter: 'Class 11',
          center: process.env.CENTER_NAME ? process.env.CENTER_NAME : '',
          streamName: 'JEE Advanced',
          languageFilter: 'English',
          subjectNameFilter: 'Chemistry ( JEE Advanced, Class 11 )',
        },
      ]

      // Step: Navigate to LMM Page
      const navigateToLmmPage = async () => {
        await expect(adminPage).toHaveURL(adminPage.pos.teacherHomePage.url)
        await adminPage.pos.teacherHomePage.navigateToInternalUser()
        await adminPage.waitForLoadState('networkidle')
        await expect(adminPage.pos.internalUserHomePage.contentManagementButton).toBeVisible()
        await adminPage.pos.internalUserHomePage.contentManagementButton.click()
        await expect(adminPage.pos.internalUserHomePage.lmmButton).toBeVisible()
        await adminPage.pos.internalUserHomePage.lmmButton.click()
        await expect(adminPage).toHaveURL(/.*lmm/)
      }

      // Step: Upload Content
      const uploadContent = async () => {
        await adminPage.pos.lmmPage.navigateToUploadContent(contentPdf)
        await adminPage.pos.lmmPage.enterDetailsOnMetaDataPage(
          'MultiTopology',
          contentNewName,
          metaData.typeName,
          metaData.subTypeName,
          metaData.learningCategoryName,
          metaData.sessionName,
          centerName,
          metaData.taxonomyName,
          metaData.className,
          metaData.subjectName,
          metaData.superTopicName,
          metaData.topicName,
          metaData.languageName,
        )
      }

      // Step: Upload Content meta data page
      const uploadContentFromMetaDataPage = async (topology) => {
        await adminPage.pos.lmmPage.clickOnAddTopology()
        await adminPage.pos.lmmPage.enterMultiTopologyDetails(
          topology.taxonomyName,
          topology.className,
          topology.subjectName,
          topology.superTopicName,
          topology.topicName,
        )
        await adminPage.pos.lmmPage.selectMultipleFilesInMetaDataPage()
        await adminPage.pos.lmmPage.validateUploadFromFillFromConsole()
      }

      // Step: Verify MultiTopology in Course Management
      const verifyMultiTopologyInCourseManagement = async (
        center,
        stream,
        className,
        subject,
        topic,
        language,
        contentName,
        centerName,
      ) => {
        await adminPage.pos.internalUserHomePage.navigateToCourseAndSyllabusPage()
        await expect(adminPage).toHaveURL(/.*course-syllabus*/)
        await adminPage.pos.courseAndSyllabusPage.applyFilterToMultiTopology(center, stream, className, courseName)
        await adminPage.pos.courseAndSyllabusPage.navigateToAttachContentPage()
        await adminPage.pos.courseAndSyllabusPage.filterUploadedContentMultiTopology(
          centerName,
          subject,
          topic,
          language,
          contentName,
        )
        await adminPage.pos.courseAndSyllabusPage.verifyContentName(contentName)
      }

      // // Execution
      await test.step('Navigate to LMM Page', navigateToLmmPage)
      await test.step('Upload content material from metadata page', uploadContent)

      if (currentEnv.isProd()) {
        await test.step('Verify add multitopology from metadata page', async () => {
          await uploadContentFromMetaDataPage(metaDataProd)
        })
      }

      if (currentEnv.isStage()) {
        await test.step('Verify add multitopology from metadata page', async () => {
          await uploadContentFromMetaDataPage(multiTopologyData[0])
        })
      }

      await test.step('Verify uploaded multitopology from metadata page with content id or name ', async () => {
        await adminPage.pos.lmmPage.validateUploadedContentName(contentNewName)
        await customExpect(15000)(
          adminPage.pos.courseAndSyllabusPage.apiLoader,
          'Verify page loading',
        ).not.toBeVisible()
        contentId = await adminPage.pos.lmmPage.validateSearchWithContentId(contentNewName)
      })

      await test.step(`Set Test Data For Cleaning Up Uploaded LMM Content `, async () => {
        lmmTest.lmmContent.push({
          id: contentId,
          name: contentNewName,
          type: type,
          subType: sub_type,
        })
        console.log(`Value updated: id=${lmmTest.lmmContent}`)
      })

      if (currentEnv.isStage()) {
        await test.step('Verify edit content and add multitopology from LMM page', async () => {
          await adminPage.pos.lmmPage.clickOnEditContentAttached()
          await adminPage.pos.lmmPage.editContentAttachedAndSave(
            multiTopologyData[1].taxonomyName,
            multiTopologyData[1].className,
            multiTopologyData[1].subjectName,
            multiTopologyData[1].superTopicName,
            multiTopologyData[1].topicName,
          )
          await slowExpect(adminPage.pos.lmmPage.addedMultiTopology).toBeVisible();
        })


        for (const topology of multiTopologyData) {
          await test.step(`Verify multitopology: ${topology.topicName} `, async () => {
            await verifyMultiTopologyInCourseManagement(
              topology.center,
              topology.streamName,
              topology.classNameFilter,
              topology.subjectNameFilter,
              topology.topicName,
              topology.languageFilter,
              contentNewName,
              centerName,
            )
          })
        }
      }

      if (currentEnv.isProd()) {

        await test.step(`Verify multitopology: ${metaData.topicName} `, async () => {
          await verifyMultiTopologyInCourseManagement(
            multiTopologyData[2].center,
            multiTopologyData[2].streamName,
            multiTopologyData[2].classNameFilter,
            multiTopologyData[2].subjectNameFilter,
            multiTopologyData[2].topicName,
            multiTopologyData[2].languageFilter,
            contentNewName,
            centerName,
          )
        })
      }
    })


    test('Verify upload QB mapping to destination center', async ({ adminPage }) => {
      const centerName = process.env.CENTER_NAME ? process.env.CENTER_NAME : ''
      const streamName = 'JEE Advanced'
      const className = 'Class 11'
      const sessionName = '2025 - 2026'

      //navigate to taxonamy page
      await test.step(`Navigate to taxonamy management page`, async () => {
        await expect(adminPage.pos.teacherHomePage.page).toHaveURL(adminPage.pos.teacherHomePage.url)
        await adminPage.goto(adminPage.pos.taxonamyPage.url) //this end point for automation purpose
      })

      //navigate to question bank page
      await test.step(`Navigate to question bank page`, async () => {
        await adminPage.pos.taxonamyPage.clickOnQuestionBank()
        await adminPage.pos.questionBankPage.verifyQBMappingPage()
      })

      //Verify the user can able to select the source (stream, session, class)
      await test.step(`Verify the user can able to select the source(stream, session, class)`, async () => {
        await adminPage.pos.questionBankPage.selectQuestionBankDropdown(streamName, sessionName, className)
      })

      //Verify the user can able to select the destination (center,stream, session, class)`
      await test.step(`Verify the user can able to select the destination (center,stream, session, class)`, async () => {
        await adminPage.pos.questionBankPage.selectCenterDropdown(centerName, streamName, sessionName, className)
      })

      //Verify uplaod qb mapping button is enabled
      await test.step(`Verify uplaod qb mapping button is enabled`, async () => {
        await adminPage.pos.questionBankPage.verifyUploadQBMappingButtonEnabled()
      })
    })

    test('Verify the functionality of edit & view qb mapping', async ({ adminPage }) => {
      const centerName = process.env.CENTER_NAME ? process.env.CENTER_NAME : ''
      const streamName = process.env.QB_STREAM ? process.env.QB_STREAM : ''
      const className = process.env.QB_CLASS ? process.env.QB_CLASS : ''
      const sessionName = process.env.QB_SESSION ? process.env.QB_SESSION : ''

      const subjectName = process.env.QB_SUBJECT_NAME ? process.env.QB_SUBJECT_NAME : ''
      const superTopicName = process.env.QB_SUPERTOPIC_NAME ? process.env.QB_SUPERTOPIC_NAME : ''
      const topicName = process.env.QB_TOPIC_NAME ? process.env.QB_TOPIC_NAME : ''

      //navigate to taxonamy page
      await test.step(`Navigate to taxonamy management page`, async () => {
        await expect(adminPage.pos.teacherHomePage.page).toHaveURL(adminPage.pos.teacherHomePage.url)
        await adminPage.goto(adminPage.pos.taxonamyPage.url) //this end point for automation purpose
      })

      //navigate to edit and view question bank mapping page
      await test.step(`Navigate to edit and view qb mapping page`, async () => {
        await adminPage.pos.taxonamyPage.clickOnQuestionBank()
        await adminPage.pos.questionBankPage.verifyQBMappingPage()
        await adminPage.pos.questionBankPage.clickOnViewMappingsButtonAndVerify()
      })

      //Verify the user can able to select the (Center, Stream, Session, Class) on Edit & View Mapping page
      await test.step(`Verify the user can able to select the (center, stream, session, class) on edit & view mapping page`, async () => {
        await adminPage.pos.questionBankPage.selectCenterDropdown(centerName, streamName, sessionName, className)
        await adminPage.pos.questionBankPage.verifyNextButtonEnabledAndClick()
      })

      //Verify the user can able to select the (Subject, Super Topic, Topic and Sub-Topic on filter)
      await test.step(`Verify the user can able to select the (subject, super topic, topic on filter)`, async () => {
        await adminPage.pos.questionBankPage.selectAndFilter(subjectName, superTopicName, topicName)
        await adminPage.pos.questionBankPage.verifyShowMappingsButtonEnabledAndClick()
        await adminPage.pos.questionBankPage.verifyAfterFilterPage()
      })

      //Verify the user can able to Edit the destination (Subject, Super Topic, Topic and Sub-Topic)
      await test.step(`Verify the user can able to edit the destination(subject, super topic, topic and sub - topic)`, async () => {
        await adminPage.pos.questionBankPage.editDestinationData(subjectName)
      })

      //Verify the user can able to delete the existing (QB Data)
      await test.step(`Verify delete pop up for single select (QB Data)`, async () => {
        await adminPage.pos.questionBankPage.verifySingleDeletePopUp()
      })

      //Verify the user can able to delete the all data
      await test.step(`Verify delete pop up for all data select (QB Data)`, async () => {
        await adminPage.pos.questionBankPage.verifyDeleteAllPopUp()
      })
    })

    test('Verify bulk content attach to library by uploading csv', async ({ adminPage, lmmTest }) => {
      test.setTimeout(80000)
      const centerName = process.env.CENTER_NAME ? process.env.CENTER_NAME : ''
      const baseURL = process.env.BASE_URL ? process.env.BASE_URL : ''
      const courseName_Lmm = process.env.COURSE_NAME ? process.env.COURSE_NAME : ''
      let contentPdf = ['test-data/uploadfile.pdf', 'test-data/uploadfile.pdf']
      let pdfContentCSVFile = 'test-data/bulk_upload_content.csv'
      let pdfContentCSVFileProd = 'test-data/bulk_upload_content_prod.csv'
      let contentNameOne = 'Automation1_' + StringUtils.generateRandomString(5)
      let contentNameTwo = 'Automation2_' + StringUtils.generateRandomString(5)
      const timezone = await PlaywrightUtils.getBrowserTimezone(adminPage)
      const currentDate = StringUtils.getCurrentTimeWithOffset('YYYY-MM-DD', 0, false, timezone)
      const phaseNumber = '1'
      const topicName = 'Chemical Equilibrium'
      let contentId1 = ''
      let contentId2 = ''
      const type = 'STUDY_MATERIALS'
      const sub_Type1 = 'RACE'
      const sub_Type2 = 'STUDY_MODULE'

      await test.step(`Navigate from teacher home page to internal user home page`, async () => {
        await expect(adminPage).toHaveURL(adminPage.pos.teacherHomePage.url)
        await adminPage.pos.teacherHomePage.navigateToInternalUser()
        await adminPage.waitForLoadState('networkidle')
        await slowExpect(adminPage).toHaveURL(/.*internal-user/)
      })

      await test.step(`Navigate to lmm page`, async () => {
        await expect(
          adminPage.pos.internalUserHomePage.contentManagementButton,
          'verify content management button is visible',
        ).toBeVisible()
        await adminPage.pos.internalUserHomePage.contentManagementButton.click()
        await expect(adminPage.pos.internalUserHomePage.lmmButton, 'verify LMM  button is visible').toBeVisible()
        await adminPage.pos.internalUserHomePage.lmmButton.click()
        await slowExpect(adminPage).toHaveURL(/.*lmm/)
      })

      await test.step(`Verify the user can able to upload the Bulk content in LMM page`, async () => {
        if (currentEnv.isProd()) {
          await adminPage.pos.lmmPage.verifyBulkUploadNewContent(
            contentPdf,
            pdfContentCSVFileProd,
            contentNameOne,
            contentNameTwo,
          )
        } else {
          await adminPage.pos.lmmPage.verifyBulkUploadNewContent(
            contentPdf,
            pdfContentCSVFile,
            contentNameOne,
            contentNameTwo,
          )
        }
      })

      await test.step(`Verify bulk content name uploaded via csv file`, async () => {
        await adminPage.pos.lmmPage.validateUploadedContentName(contentNameOne)
        contentId1 = await adminPage.pos.lmmPage.validateSearchWithContentId(contentNameOne)

        await expect(adminPage.pos.lmmPage.clearButton, 'Verify clear button is visible').toBeVisible()
        await adminPage.pos.lmmPage.clearButton.click()
        await adminPage.waitForLoadState('networkidle')
        await adminPage.pos.lmmPage.validateUploadedContentName(contentNameTwo)
        contentId2 = await adminPage.pos.lmmPage.validateSearchWithContentId(contentNameTwo)
      })

      await test.step(`Set Test Data For Cleaning Up Uploaded LMM Content`, async () => {
        lmmTest.lmmContent.push(
          {
            id: contentId1,
            name: contentNameOne,
            type: type,
            subType: sub_Type1,
          },
          {
            id: contentId2,
            name: contentNameTwo,
            type: type,
            subType: sub_Type2,
          }
        )
      })

      await test.step(`Navigate to course management page and apply filter for first bulk content`, async () => {
        await adminPage.pos.internalUserHomePage.navigateToCourseAndSyllabusPage()
        await adminPage.waitForLoadState('networkidle')
        await expect(adminPage).toHaveURL(/.*course-syllabus*/)
        await adminPage.pos.courseAndSyllabusPage.applyFilterToAutomationCenter(centerName, courseName_Lmm)
      })

      await test.step(`Navigate to attach content page and apply filter`, async () => {
        await adminPage.pos.courseAndSyllabusPage.navigateToAttachContentPage()
        await adminPage.pos.courseAndSyllabusPage.filterUploadedContent(topicName, baseURL)
      })

      await test.step(`Verify uploaded first bulk content from course page`, async () => {
        await adminPage.pos.courseAndSyllabusPage.verifyContentName(contentNameOne)
      })

      await test.step(`Verify assigned the uploaded first bulk content to coursee`, async () => {
        await adminPage.pos.courseAndSyllabusPage.assignTheUploadedContentToCourse(
          contentNameOne,
          phaseNumber,
          currentDate,
        )
        await adminPage.pos.courseAndSyllabusPage.verifyUploadSuccessful()
      })

      await test.step(`Navigate to course management page and apply filter for second bulk content`, async () => {
        await adminPage.pos.internalUserHomePage.navigateToCourseAndSyllabusPage()
        await adminPage.waitForLoadState('networkidle')
        await expect(adminPage).toHaveURL(/.*course-syllabus*/)
        await adminPage.pos.courseAndSyllabusPage.applyFilterToAutomationCenter(centerName, courseName_Lmm)
      })

      await test.step(`Navigate to attach content page and apply filter`, async () => {
        await adminPage.pos.courseAndSyllabusPage.navigateToAttachContentPage()
        await adminPage.pos.courseAndSyllabusPage.filterUploadedContent(topicName, baseURL)
      })

      await test.step(`Verify uploaded second bulk content from course page`, async () => {
        await adminPage.pos.courseAndSyllabusPage.verifyContentName(contentNameTwo)
      })

      await test.step(`Verify assigned the uploaded second bulk content to coursee`, async () => {
        await adminPage.pos.courseAndSyllabusPage.assignTheUploadedContentToCourse(
          contentNameTwo,
          phaseNumber,
          currentDate,
        )
        await adminPage.pos.courseAndSyllabusPage.verifyUploadSuccessful()
      })
    })


    test('Verify filters from lmm home page', async ({ adminPage }) => {
      const centerName = process.env.CENTER_NAME ? process.env.CENTER_NAME : ''
      let subjectName = 'Chemistry'
      const superTopicName = 'Physical Chemistry'
      const topicName = 'Chemical Equilibrium'
      const taxonamyName = 'ADPL_STREAM_JEE_MAIN_ADVANCED_2024_2025'
      const className = 'Class 11'

      await test.step(`Verify the user able to apply the filter on LMM page`, async () => {
        await adminPage.pos.lmmPage.goto()
        await adminPage.waitForLoadState('networkidle')
        await slowExpect(adminPage).toHaveURL(/.*lmm/)
        await adminPage.pos.lmmPage.clickOnFilterButton()
        await adminPage.pos.lmmPage.applyFilterOnLmmPageTillClass(centerName, taxonamyName, className)
        await adminPage.pos.lmmPage.validateFilteronLmmPageTillClass(centerName, taxonamyName, className)
        await adminPage.pos.lmmPage.clickOnFilterButton()
        await adminPage.pos.lmmPage.applyFilterOnLmmPageTillTopic(subjectName, superTopicName, topicName)
        await adminPage.pos.lmmPage.validateFilteronLmmPageTillTopic(subjectName, superTopicName, topicName)
      })
    })


    test('Verify the special batch content is displayed on the Student side', async ({ adminPage, testData, lmmTest }) => {
      let specialBatch

      /*Cancelling all the classes*/
      await test.step(`Creating special batch`, async () => {
        specialBatch = await adminPage.apis.resourceManagement.createSpecialBatch()
        await adminPage.apis.resourceManagement.addSyllabusToBatch(specialBatch.id)
        await adminPage.apis.resourceManagement.enrollToSpecialBatch(specialBatch.id, testData.student.id)
        console.log('Special batch details', specialBatch.name)
      })

      const baseURL = process.env.BASE_URL ? process.env.BASE_URL : ''
      let pdfContentCSVFileProd = 'test-data/single_pdf_sb_prod.csv'
      let contentPdf = 'test-data/uploadfile.pdf'
      let pdfContentCSVFile = 'test-data/single_pdf_content.csv'
      let contentNewName = 'Automation_' + StringUtils.generateRandomString(5)
      const topicName = 'Chemical Equilibrium'
      const topicNameProd = 'Circle'
      const type = 'STUDY_MATERIALS'
      const sub_type = 'STUDY_MODULE'
      let contentId = ''

      await test.step(`Navigate from teacher home page to internal user home page`, async () => {
        await expect(adminPage).toHaveURL(adminPage.pos.teacherHomePage.url)
        await adminPage.pos.teacherHomePage.navigateToInternalUser()
        await adminPage.waitForLoadState('networkidle')
        await slowExpect(adminPage).toHaveURL(/.*internal-user/)
      })

      await test.step(`Navigate to lmm page`, async () => {
        await expect(
          adminPage.pos.internalUserHomePage.contentManagementButton,
          'verify content management button is visible',
        ).toBeVisible()
        await adminPage.pos.internalUserHomePage.contentManagementButton.click()
        await expect(adminPage.pos.internalUserHomePage.lmmButton, 'verify LMM  button is visible').toBeVisible()
        await adminPage.pos.internalUserHomePage.lmmButton.click()
        await slowExpect(adminPage).toHaveURL(/.*lmm/)
      })

      await test.step(`Verify upload material content to library via csv file`, async () => {
        if (currentEnv.isProd()) {
          await adminPage.pos.lmmPage.uploadNewContent(contentPdf, pdfContentCSVFileProd, contentNewName)
        } else {
          await adminPage.pos.lmmPage.uploadNewContent(contentPdf, pdfContentCSVFile, contentNewName)
        }
      })

      await test.step(`Verify content name uploaded via csv file`, async () => {
        await adminPage.pos.lmmPage.validateUploadedContentName(contentNewName)
        contentId = await adminPage.pos.lmmPage.validateSearchWithContentId(contentNewName)
      })

      await test.step(`Set Test Data For Cleaning Up Uploaded LMM Content `, async () => {
        lmmTest.lmmContent.push({
          id: contentId,
          name: contentNewName,
          type: type,
          subType: sub_type,
        })
        console.log(`Value updated: id=${lmmTest.lmmContent}`)
      })

      await test.step(`Navigate to batch management page and apply filter for first topology`, async () => {
        //await customExpect(15000)(adminPage.pos.batchManagementPage.apiLoader, "Verify page loading").not.toBeVisible();
        await adminPage.pos.internalUserHomePage.navigateToBatchManagementPage()
        await expect(adminPage).toHaveURL(/.*batch*/)
        await adminPage.waitForTimeout(1000); //wait is requried due to websocket
        await customExpect(15000)(
          adminPage.pos.batchManagementPage.apiLoader, 'Verify page loading').not.toBeVisible()

        await adminPage.pos.batchManagementPage.clickOnSpecialBatchTab(baseURL)
        await customExpect(15000)(
          adminPage.pos.batchManagementPage.apiLoader,
          'Verify page loading',
        ).not.toBeVisible()
      })

      await test.step(`Navigate to special batch and apply filter`, async () => {
        await adminPage.pos.batchManagementPage.applyFilterTo2025AccadamicSession();
        await adminPage.pos.batchManagementPage.validateSearchSpecialBatch(specialBatch.name)
        await adminPage.pos.batchManagementPage.clickOnContentDetailsButton()
      })

      await test.step(`Verify uploaded content from batch management page`, async () => {
        if (currentEnv.isProd()) {
          await adminPage.pos.courseAndSyllabusPage.filterUploadedContent(topicNameProd, baseURL)
        } else {
          await adminPage.pos.courseAndSyllabusPage.filterUploadedContent(topicName, baseURL)
        }
        await adminPage.pos.courseAndSyllabusPage.verifyContentName(contentNewName)
      })

      await test.step(`Verify assigned the uploaded subject level material content to coursee`, async () => {
        await adminPage.pos.courseAndSyllabusPage.validateAttachContentToSpecialBatch(contentNewName)
        await adminPage.pos.courseAndSyllabusPage.verifyUploadSuccessful()
      })

      await test.step(`Deleting the special batch`, async () => {
        specialBatch = await adminPage.apis.resourceManagement.deleteSpecialBatch(specialBatch.id)
        console.log('Special batch details', specialBatch.name)
      })
    })

    if (currentEnv.isStage()) {
      test('Verify the functionality of Flashcard', async ({ adminPage, studentPage, testData }) => {
        // if (currentEnv.isProd()) {
        //   test.setTimeout(120000)
        // }
        // else {
        test.setTimeout(100000)
        // }
        const mathsSubject = "Maths";
        const fundamentalsOfAlgebra = "Fundamentals of Algebra";
        const flashCardTotalCount = 20;
        const baseURL = process.env.BASE_URL_WEB ? process.env.BASE_URL_WEB : ''
        const taxonamyId = process.env.TAXONAMY_ID_LIVE ? process.env.TAXONAMY_ID_LIVE : ''
        const nodeId = process.env.NODE_ID ? process.env.NODE_ID : ''

        /* Login to the student side with mobile number */
        await test.step(`Login to the student side`, async () => {
          await studentPage.login();
          await slowExpect(studentPage.pos.homePage.exploreMenu, 'verifying "explore side menu" text should be visible').toBeVisible();
        });

        //Verify the Flashcard under Quick Actions widget
        await test.step(`Verify the flashcard under Quick Actions widget`, async () => {
          await slowExpect(studentPage.pos.homePage.quickActionsTitleText, "Verify quick actions title is visible").toBeVisible();
          await slowExpect(studentPage.pos.homePage.flashCardButton, "Verify flashcard button is visible").toBeVisible();
          await studentPage.pos.homePage.flashCardButton.click();
        });

        await test.step(`Verify select subject and topic from flashcard page`, async () => {
          await studentPage.pos.flashCardPage.validateSelectSubjectAndTopic();
          await studentPage.pos.flashCardPage.checkAndClickSubjectUnderSelectSubject(mathsSubject!);
          await studentPage.pos.flashCardPage.checkAndClickTopicUnderSelectTopic(fundamentalsOfAlgebra!);
        });

        await test.step(`Verify flashcard page details`, async () => {
          await studentPage.pos.flashCardPage.clickOnStartButton(baseURL);
          const show_onboarding = await adminPage.apis.resourceManagement.startFlashcardsSession(testData.batch.id, nodeId, taxonamyId);
          if (show_onboarding) {
            await studentPage.pos.flashCardPage.clickOnGetStartButton();
          }
          const getcount = await studentPage.pos.flashCardPage.getCardSwipeCount();
          await studentPage.pos.flashCardPage.verifyFlashCardPage(fundamentalsOfAlgebra, flashCardTotalCount, getcount);
        });

        await test.step(`Verify the user can able to swipe and close the flashcards`, async () => {
          await studentPage.pos.flashCardPage.clickOnSwipeIKnowThisButtonTimes(1);
          await studentPage.pos.flashCardPage.clickOnSwipeStillLearningButtonTimes(1);
          await expect(studentPage.pos.flashCardPage.afterSwipeCardCount("3"), "Verify the after count swipe card count is visible").toBeVisible();
          await studentPage.pos.flashCardPage.clickOnCloseFlashCardIcon();
          await studentPage.pos.flashCardPage.validateSwipeCards("1", "1");
          await studentPage.pos.flashCardPage.clickOnCloseFlashCardButton();
        });


        await test.step(`Verify the resumption flashcard still learning and unseen cards is displayed on the home page`, async () => {
          await studentPage.pos.homePage.validateContinueRevisingFlashCards(mathsSubject, fundamentalsOfAlgebra);
          await studentPage.pos.homePage.validateResumeFlashCardsPopUp();
          await studentPage.pos.homePage.clickOnStillLearningAndUnseenCardsSelect();
          await expect(studentPage.pos.flashCardPage.afterSwipeCardCount("2"), "Verify the after count swipe card count is visible").toBeVisible();
          await expect(studentPage.pos.flashCardPage.closeFlashCardIcon, "Verify close icon is visible").toBeVisible();
          studentPage.pos.flashCardPage.closeFlashCardIcon.click();
          await studentPage.pos.flashCardPage.clickOnCloseFlashCardButton();
        });

        await test.step(`Verify the resumption flashcard unseen cards is displayed on the home page`, async () => {
          await studentPage.pos.homePage.validateContinueRevisingFlashCards(mathsSubject, fundamentalsOfAlgebra);
          await studentPage.pos.homePage.validateResumeFlashCardsPopUp();
          await studentPage.pos.homePage.clickOnUnseenardsSelect();
          await expect(studentPage.pos.flashCardPage.afterSwipeCardCount("2"), "Verify the after count swipe card count is visible").toBeVisible();

        });

        await test.step(`Verify swipe card till that was quick title text`, async () => {
          await studentPage.pos.flashCardPage.clickOnSwipeIKnowThisButtonTimes(15);
          await studentPage.pos.flashCardPage.clickOnSwipeStillLearningButtonTimes(4);
          await studentPage.pos.flashCardPage.validateThatWasQuick();
        });

        await test.step(`Verify the user can able to complete the resumed flashcards-unseen cards only`, async () => {
          const getcountDeck = await studentPage.pos.flashCardPage.getCardSwipeCount();
          if (currentEnv.isProd()) {
            await studentPage.pos.flashCardPage.clickOnSwipeIKnowThisButtonTimes(getcountDeck);
          }
          else {
            await studentPage.pos.flashCardPage.clickOnSwipeIKnowThisButtonTimes(10);
            await studentPage.pos.flashCardPage.validateCompletedFlashCards();
          }
        });

        if (currentEnv.isStage()) {
          await test.step(`Verify resume flashcards title after complete the flashcards`, async () => {
            await slowExpect(studentPage.pos.homePage.exploreMenu, 'verifying "explore side menu" text should be visible').toBeVisible();
            await slowExpect(studentPage.pos.homePage.continueRevisingFlashCardsTitle, "Verify continue revising flashcards title is not visible after completing flashcards").toBeHidden();
          });
        }

      });

      test('Verify admin able to upload subject level content from meta data page for new session 25_26', async ({ adminPage, lmmTest }) => {
        test.setTimeout(80000)
        const centerName = process.env.CENTER_NAME ? process.env.CENTER_NAME : ''
        const baseURL = process.env.BASE_URL ? process.env.BASE_URL : ''
        const courseName_Lmm = process.env.JEE_COURSE_25_26 ? process.env.JEE_COURSE_25_26 : ''

        // const courseName_Lmm = "Automation Testing";
        let contentPdf = 'test-data/uploadfile.pdf'
        let contentNameSubjectLevel = 'Automation_' + StringUtils.generateRandomString(5)
        let subjectName = 'Chemistry'
        const superTopicName = 'Physical Chemistry'
        const topicName = 'Chemical Equilibrium'
        const firstTopicName = 'Atomic Structure'
        const timezone = await PlaywrightUtils.getBrowserTimezone(adminPage)
        const currentDate = StringUtils.getCurrentTimeWithOffset('YYYY-MM-DD', 0, false, timezone)
        const phaseNumber = '1'
        const uploadType = 'SubjectLevel'

        const subjectLevelTopicName = 'None'
        const taxonamyName = 'ALLEN_STREAM_JEE_MAIN_ADVANCED_2025_2026'

        const typeName = 'Study Materials'
        const subTypeName = 'Study Module'
        const learningCategoryNameName = 'Instructed'
        const sessionName = '04/2025 - 03/2026'
        const className = 'Class 11'
        const languageName = 'ENGLISH'
        const contentName = 'Testing'
        const type = 'STUDY_MATERIALS'
        const sub_type = 'STUDY_MODULE'
        let contentId = ''

        await test.step(`Navigate from teacher home page to internal user home page`, async () => {
          await expect(adminPage).toHaveURL(adminPage.pos.teacherHomePage.url)
          await adminPage.pos.teacherHomePage.navigateToInternalUser()
          await adminPage.waitForLoadState('networkidle')
          await slowExpect(adminPage).toHaveURL(/.*internal-user/)
        })

        await test.step(`Navigate to lmm page`, async () => {
          await expect(
            adminPage.pos.internalUserHomePage.contentManagementButton,
            'Verify content management button is visible',
          ).toBeVisible()
          await adminPage.pos.internalUserHomePage.contentManagementButton.click()
          await expect(adminPage.pos.internalUserHomePage.lmmButton, 'Verify LMM  button is visible').toBeVisible()
          await adminPage.pos.internalUserHomePage.lmmButton.click()
          await slowExpect(adminPage).toHaveURL(/.*lmm/)
        })

        await test.step(`Upload content material from meta data page in console`, async () => {
          await adminPage.pos.lmmPage.navigateToUploadContent(contentPdf)
          await adminPage.pos.lmmPage.enterDetailsOnMetaDataPage(
            uploadType,
            contentNameSubjectLevel,
            typeName,
            subTypeName,
            learningCategoryNameName,
            sessionName,
            centerName,
            taxonamyName,
            className,
            subjectName,
            superTopicName,
            topicName,
            languageName,
          )
        })

        await test.step(`Verify click on upload content`, async () => {
          await adminPage.pos.lmmPage.validateUploadFromFillFromConsole()
        })

        await test.step(`Verify content name uploaded - subject level from meta data page`, async () => {
          await adminPage.pos.lmmPage.validateUploadedContentName(contentNameSubjectLevel)
          contentId = await adminPage.pos.lmmPage.validateSearchWithContentId(contentNameSubjectLevel)
        })

        await test.step(`Set Test Data For Cleaning Up Uploaded LMM Content `, async () => {
          lmmTest.lmmContent.push({
            id: contentId,
            name: contentNameSubjectLevel,
            type: type,
            subType: sub_type,
          })
          console.log(`Value updated: id=${lmmTest.lmmContent}`)
        })

        await test.step('. Verify search the content name with Sort  ', async () => {
          await adminPage.pos.lmmPage.validateSearchWithSort(contentName)
        })

        await test.step(`Navigate to course management page and apply filter`, async () => {
          await adminPage.pos.internalUserHomePage.navigateToCourseAndSyllabusPage()
          await expect(adminPage).toHaveURL(/.*course-syllabus*/)
          await customExpect(15000)(adminPage.pos.courseAndSyllabusPage.apiLoader, "Verify page loading").not.toBeVisible();
          await adminPage.pos.courseAndSyllabusPage.applyFilterToNewSessionCourse(centerName, courseName_Lmm)
        })

        await test.step(`Navigate to attach content page and apply filter`, async () => {
          await adminPage.pos.courseAndSyllabusPage.navigateToAttachContentToCoursePage()
          await adminPage.pos.courseAndSyllabusPage.filterUploadedContent(subjectLevelTopicName, baseURL)
        })

        await test.step(`Verify uploaded content from course page`, async () => {
          await adminPage.pos.courseAndSyllabusPage.verifyContentName(contentNameSubjectLevel)
        })

        await test.step(`Verify assigned the uploaded subject level material content to coursee`, async () => {
          await adminPage.pos.courseAndSyllabusPage.assignTheUploadedContentToNewSessionCourse(
            contentNameSubjectLevel
          );
        })
      });

      test('Verify student is able to access revision notes through study essentials', async ({ studentPage }) => {
        await test.step(`Login to the student side`, async () => {
          await studentPage.login();
          await slowExpect(studentPage.pos.homePage.exploreMenu, 'Verify "explore side menu" text is visible').toBeVisible();
        });

        await test.step(`Verify the Revision Notes under Study Essential widget`, async () => {
        await studentPage.pos.homePage.verifyRevisionNotesCardUnderStudyEssentials();
        await studentPage.pos.revisionNotesPage.page.waitForLoadState('domcontentloaded');
          await expect(studentPage.pos.revisionNotesPage.page).toHaveURL(studentPage.pos.revisionNotesPage.url);
        });

        await test.step(`Verify select subject and topic from revision notes page`, async () => {
          await expect(studentPage.pos.revisionNotesPage.revisionNotesTitle, 'Verify revision notes title').toBeVisible();
          await studentPage.pos.revisionNotesPage.selectSubjectAndTopic();
          await studentPage.pos.revisionNotesPage.verifyRevisionNotesContent();
        });
      })

      test('Ensure that student can able to access the Revision Notes through Revise tab', async ({ studentPage }) => {
        const subjectName = 'Chemistry';
        await test.step(`Login to the student side`, async () => {
          await studentPage.login();
          await slowExpect(studentPage.pos.homePage.exploreMenu, 'Verify "explore side menu" text is visible').toBeVisible();
        });

        await test.step(`Verify the revision notes under revise tab`, async () => {
          await studentPage.pos.homePage.verifyRevisionNotesCardUnderReviseTab(subjectName);
          await studentPage.pos.subjectDetailsPage.page.waitForLoadState('domcontentloaded');
          await slowExpect(studentPage.pos.subjectDetailsPage.reviseTab).toBeVisible();
        });

        await test.step(`Verify the Revision Notes under Revise Tab`, async () => {
          await studentPage.pos.subjectDetailsPage.navigateToRevisionNotes();
          await studentPage.pos.revisionNotesPage.page.waitForLoadState('domcontentloaded');
          await slowExpect(studentPage.pos.revisionNotesPage.selectChapter, 'Verify chapter name is visible').toBeVisible();
          await studentPage.pos.revisionNotesPage.selectChapter.click();
          await studentPage.pos.revisionNotesPage.verifyRevisionNotesContent();
        })
      });

      test('Verify student is able to access the revision notes from the revision notes tile', async ({ studentPage }) => {
        await test.step(`Login to the student side`, async () => {
          await studentPage.login();
          await slowExpect(studentPage.pos.homePage.exploreMenu, 'Verify "explore side menu" text is visible').toBeVisible();
        });

        await test.step(`Verify the Revision Notes under Study Essential widget`, async () => {
          await studentPage.pos.homePage.verifyRevisionNotesCardUnderStudyEssentials();
          await studentPage.pos.revisionNotesPage.page.waitForLoadState('domcontentloaded');
          await expect(studentPage.pos.revisionNotesPage.page).toHaveURL(studentPage.pos.revisionNotesPage.url);
        });

        await test.step(`Verify select subject and topic from revision notes page`, async () => {
          await expect(studentPage.pos.revisionNotesPage.revisionNotesTitle, 'Verify revision notes title').toBeVisible();
          await studentPage.pos.revisionNotesPage.selectSubjectAndTopic();
          await studentPage.pos.revisionNotesPage.verifyRevisionNotesContent();
        });

        await test.step('Verify student is able to switch topics', async () => {
          await studentPage.pos.revisionNotesPage.switchTopics();
          await studentPage.pos.revisionNotesPage.page.waitForLoadState('domcontentloaded');
        })

        await test.step('Verify student is able to interact with images and tables on revision notes page', async () => {
          await studentPage.pos.revisionNotesPage.verifyImageAndTableInteraction();
        })
      });

      test('Verify the student can able to Highlight and remove the Assets on Revision Notes (Text, Image, Question)', async ({ studentPage }) => {
        const subjectName = 'Chemistry';
        await test.step(`Login to the student side`, async () => {
          await studentPage.login();
          await slowExpect(studentPage.pos.homePage.exploreMenu, 'Verify "explore side menu" text is visible').toBeVisible();
        });

        await test.step(`Verify the revision notes under revise tab`, async () => {
          await studentPage.pos.homePage.verifyRevisionNotesCardUnderReviseTab(subjectName);
          await studentPage.pos.subjectDetailsPage.page.waitForLoadState('domcontentloaded');
          await slowExpect(studentPage.pos.subjectDetailsPage.reviseTab).toBeVisible();
        });

        await test.step(`Verify the Revision Notes under Revise Tab`, async () => {
          await studentPage.pos.subjectDetailsPage.navigateToRevisionNotes();
          await studentPage.pos.revisionNotesPage.page.waitForLoadState('networkidle');
          await slowExpect(studentPage.pos.revisionNotesPage.selectChapter, 'Verify chapter name is visible').toBeVisible();
          await studentPage.pos.revisionNotesPage.selectChapter.click();
          await studentPage.pos.revisionNotesPage.verifyRevisionNotesContent();
        });

        await test.step(`Verify Your Highlights page and demo video is visible`, async () => {
          await studentPage.pos.revisionNotesPage.verifyHighlightPage();
        });
       await test.step('navigate to revision notes page', async () => {
          await slowExpect(studentPage.pos.revisionNotesPage.selectChapter, 'Verify chapter name is visible').toBeVisible();
          await studentPage.pos.revisionNotesPage.selectChapter.click();
          await studentPage.pos.revisionNotesPage.verifyRevisionNotesContent();
       })
       await test.step('highlight the text', async () => {
        await slowExpect(studentPage.pos.revisionNotesPage.highlightnotestext, 'Verify text is visible').toBeVisible();
        await studentPage.pos.revisionNotesPage.highlightnotestext.dblclick();
        await expect(studentPage.pos.revisionNotesPage.starButton, 'Verify star button is visible').toBeVisible();
        await studentPage.pos.revisionNotesPage.starButton.click();
        await expect(studentPage.pos.revisionNotesPage.highlightText, 'Verify highlight text is visible').toBeVisible();
        await studentPage.pos.revisionNotesPage.highlightText.click();
        await studentPage.pos.revisionNotesPage.page.waitForLoadState('domcontentloaded');
        await slowExpect(studentPage.pos.revisionNotesPage.highlightnotestext, 'Verify highlight text is visible').toBeVisible();
       });
       await test.step('navigate to revision notes page', async () => {
        await slowExpect(studentPage.pos.revisionNotesPage.selectChapter, 'Verify chapter name is visible').toBeVisible();
        await studentPage.pos.revisionNotesPage.selectChapter.click();
        await studentPage.pos.revisionNotesPage.verifyRevisionNotesContent();
     })
       await test.step('highlight the image', async () => {
        await slowExpect(studentPage.pos.revisionNotesPage.image, 'Verify image is visible').toBeVisible();
        await studentPage.pos.revisionNotesPage.image.click({
          clickCount: 2,
          position: { x: 10, y: 10 }
        });
        await expect(studentPage.pos.revisionNotesPage.doubtButton, 'Verify doubt button is visible').toBeVisible();
        await studentPage.pos.revisionNotesPage.doubtButton.click();
        await expect(studentPage.pos.revisionNotesPage.highlightText, 'Verify highlight text is visible').toBeVisible();
        await studentPage.pos.revisionNotesPage.highlightText.click();
        await studentPage.pos.revisionNotesPage.page.waitForLoadState('domcontentloaded');
        await slowExpect(studentPage.pos.revisionNotesPage.image, 'Verify image is visible').toBeVisible();
       })

       await test.step('navigate to revision notes page', async () => {
        await slowExpect(studentPage.pos.revisionNotesPage.selectChapter, 'Verify chapter name is visible').toBeVisible();
        await studentPage.pos.revisionNotesPage.selectChapter.click();
        await studentPage.pos.revisionNotesPage.verifyRevisionNotesContent();
     })
     await test.step('remove the highlight', async () => {
      await studentPage.pos.revisionNotesPage.page.waitForLoadState('domcontentloaded');
      await slowExpect(studentPage.pos.revisionNotesPage.highlightnotestext, 'Verify text is visible').toBeVisible();
      await studentPage.pos.revisionNotesPage.highlightnotestext.dblclick();
      await expect(studentPage.pos.revisionNotesPage.starButton, 'Verify star button is visible').toBeVisible();
      await studentPage.pos.revisionNotesPage.starButton.click();
      await expect(studentPage.pos.revisionNotesPage.image, 'Verify image is visible').toBeVisible();
      await studentPage.pos.revisionNotesPage.image.click({
        clickCount: 2,
        position: { x: 10, y: 10 }
      });
      await expect(studentPage.pos.revisionNotesPage.doubtButton, 'Verify doubt button is visible').toBeVisible();
      await studentPage.pos.revisionNotesPage.doubtButton.click();
      await studentPage.pos.revisionNotesPage.page.waitForLoadState('domcontentloaded');
     })

      await test.step('highlight the question', async () => {
      await expect(studentPage.pos.revisionNotesPage.questionText, 'Verify question text is visible').toBeVisible();
      await studentPage.pos.revisionNotesPage.questionText.dblclick();
      await expect(studentPage.pos.revisionNotesPage.starButton, 'Verify star button is visible').toBeVisible();
      await studentPage.pos.revisionNotesPage.starButton.click();
      await studentPage.pos.revisionNotesPage.page.waitForLoadState('domcontentloaded');
      await expect(studentPage.pos.revisionNotesPage.highlightText, 'Verify highlight text is visible').toBeVisible();
      await studentPage.pos.revisionNotesPage.highlightText.click();
      await studentPage.pos.revisionNotesPage.page.waitForLoadState('domcontentloaded');
      await studentPage.pos.revisionNotesPage.page.waitForTimeout(500);
      await slowExpect(studentPage.pos.revisionNotesPage.questionidSearch, 'Verify question text is visible').toBeVisible();
      })

      await test.step('navigate to revision notes page', async () => {
        await slowExpect(studentPage.pos.revisionNotesPage.selectChapter, 'Verify chapter name is visible').toBeVisible();
        await studentPage.pos.revisionNotesPage.selectChapter.click();
        await studentPage.pos.revisionNotesPage.verifyRevisionNotesContent();
     })

      await test.step('remove the highlight', async () => {
        await studentPage.pos.revisionNotesPage.page.waitForLoadState('domcontentloaded');
        await slowExpect(studentPage.pos.revisionNotesPage.questionText, 'Verify question text is visible').toBeVisible();
        await studentPage.pos.revisionNotesPage.questionText.dblclick();
        await expect(studentPage.pos.revisionNotesPage.starButton, 'Verify star button is visible').toBeVisible();
        await studentPage.pos.revisionNotesPage.starButton.click();
      })

      });

      test('Ensure that student can able to access the Specialised Notes', async ({ studentPage }) => {
        const subjectName = 'Chemistry';
        await test.step(`Login to the student side`, async () => {
          await studentPage.login();
          await slowExpect(studentPage.pos.homePage.exploreMenu, 'Verify "explore side menu" text is visible').toBeVisible();
        });
        
  
        await test.step(`Verify the revision notes under revise tab`, async () => {
          await studentPage.pos.homePage.verifyRevisionNotesCardUnderReviseTab(subjectName);
          await studentPage.pos.subjectDetailsPage.page.waitForLoadState('domcontentloaded');
          await slowExpect(studentPage.pos.subjectDetailsPage.reviseTab).toBeVisible();
        });
  
        await test.step(`Verify the Revision Notes under Revise Tab`, async () => {
          await studentPage.pos.subjectDetailsPage.navigateToRevisionNotes();
          await studentPage.pos.revisionNotesPage.page.waitForLoadState('networkidle');
          await slowExpect(studentPage.pos.revisionNotesPage.selectChapter, 'Verify chapter name is visible').toBeVisible();
          await studentPage.pos.revisionNotesPage.selectChapter.click();
          await studentPage.pos.revisionNotesPage.verifyRevisionNotesContent();
        });
        await test.step('verify the specialized notes header, common confusions, diagrams and graphs', async () => {
          await expect(studentPage.pos.revisionNotesPage.specializedNotesHeader, 'Verify specialized notes header is visible').toBeVisible();
          await expect(studentPage.pos.revisionNotesPage.commonConfusions, 'Verify common confusions is visible').toBeVisible();
          // await expect(studentPage.pos.revisionNotesPage.diagramsAndGraphs, 'Verify diagrams and graphs is visible').toBeVisible();
        });
        await test.step('verify and select key pointers', async () => {
          await expect(studentPage.pos.revisionNotesPage.keyPointers, 'Verify key pointers is visible').toBeVisible();
          await studentPage.pos.revisionNotesPage.keyPointers.click();
          await expect(studentPage.pos.revisionNotesPage.keyPointersHeader, 'Verify key pointers header is visible').toBeVisible();
          await expect(studentPage.pos.revisionNotesPage.keyPointersNoteBody, 'Verify key pointers note body is visible').toBeVisible();
          await expect(studentPage.pos.revisionNotesPage.nextTopicDiv, 'Verify next topic is visible').toBeVisible();
        });
        
        
      });

      test('Ensure that Still Learning flashcards are displayed on the Improvement book page', async ({ studentPage }) => {
        await test.step(`Login to the student side`, async () => {
          await studentPage.login();
          await slowExpect(studentPage.pos.homePage.exploreMenu, 'Verify "explore side menu" text is visible').toBeVisible();
        });
        await test.step(`Verify the flashcard under Quick Actions widget`, async () => {
        await slowExpect(studentPage.pos.homePage.quickActionsTitleText, "Verify quick actions title is visible").toBeVisible();
          await slowExpect(studentPage.pos.homePage.flashCardButton, "Verify flashcard title is visible").toBeVisible();
          await studentPage.pos.homePage.flashCardButton.click();
          await studentPage.pos.flashCardPage.page.waitForLoadState('networkidle');
        });

        await test.step(`Attempt the flashcard with Still learning`, async () => {
          await slowExpect(studentPage.pos.flashCardPage.subjectText('Chemistry'), "Verify chemistry subject is visible").toBeVisible();
          await studentPage.pos.flashCardPage.subjectText('Chemistry').click();
          await studentPage.pos.flashCardPage.checkAndClickTopicUnderSelectTopic('Atomic Structure');
          await studentPage.pos.flashCardPage.clickOnStartButton(studentPage.pos.flashCardPage.url);
          await studentPage.pos.flashCardPage.getCardCount('stillLearning');
          await slowExpect(studentPage.pos.flashCardPage.closeFlashCardIcon, "Verify close button is visible").toBeVisible();
          await studentPage.pos.flashCardPage.closeFlashCardIcon.click();
          await slowExpect(studentPage.pos.flashCardPage.closeButton, "Verify close button is visible").toBeVisible();
          await studentPage.pos.flashCardPage.closeButton.click();
          await studentPage.pos.flashCardPage.page.waitForLoadState('networkidle');
        });
        
        await test.step(`Click on the Improvement book on Quick Actions`, async () => {
          await slowExpect(studentPage.pos.homePage.improvementBookButton, "Verify improvement book title is visible").toBeVisible();
          await studentPage.pos.homePage.improvementBookButton.click();
          await studentPage.pos.revisionNotesPage.page.waitForLoadState('networkidle');
          await studentPage.pos.revisionNotesPage.page.waitForTimeout(500);
        });

        await test.step(`Verify the flashcard under Improvement book page`, async () => {
          await slowExpect(studentPage.pos.revisionNotesPage.selectTopic('Atomic Structure'), "Verify atomic structure topic is visible").toBeVisible();
          await slowExpect(studentPage.pos.homePage.atomicStructureButton, "Verify atomic structure button is visible").toBeVisible();
          await studentPage.pos.homePage.atomicStructureButton.click();
          await slowExpect(studentPage.pos.homePage.flashcardsToBeReviewed, "Verify flashcards to be reviewed is visible").toBeVisible();
          await slowExpect(studentPage.pos.homePage.viewButton, "Verify view button is visible").toBeVisible();
          await studentPage.pos.homePage.viewButton.click();
          await slowExpect(studentPage.pos.flashCardPage.stillLearningText, "Verify still learning text is visible").toBeVisible();
          await slowExpect(studentPage.pos.homePage.reviseCard, "Verify revise card is visible").toBeVisible();
          await studentPage.pos.homePage.reviseCard.click();
          await studentPage.pos.flashCardPage.getCardCount('iKnowThis');
          await slowExpect(studentPage.pos.flashCardPage.closeFlashCardIcon, "Verify close button is visible").toBeVisible();
          await studentPage.pos.flashCardPage.closeFlashCardIcon.click();
          await slowExpect(studentPage.pos.flashCardPage.closeButton, "Verify close button is visible").toBeVisible();
          await studentPage.pos.flashCardPage.closeButton.click();
          await studentPage.pos.flashCardPage.page.waitForLoadState('networkidle');
          await slowExpect(studentPage.pos.revisionNotesPage.selectTopic('Atomic Structure'), "Verify atomic structure topic is visible").toBeVisible();
          await studentPage.pos.revisionNotesPage.selectTopic('Atomic Structure').click();
        });
        
      });

      test('Ensure that Flashcard count is increased when resume the Flashcard', async ({ studentPage }) => {
        let count ;
        await test.step(`Login to the student side`, async () => {
          await studentPage.login();
          await slowExpect(studentPage.pos.homePage.exploreMenu, 'Verify "explore side menu" text is visible').toBeVisible();
        });

        await test.step(`Verify the flashcard under Quick Actions widget`, async () => {
        await slowExpect(studentPage.pos.homePage.quickActionsTitleText, "Verify quick actions title is visible").toBeVisible();
          await slowExpect(studentPage.pos.homePage.flashCardButton, "Verify flashcard title is visible").toBeVisible();
          await studentPage.pos.homePage.flashCardButton.click();
          await studentPage.pos.flashCardPage.page.waitForLoadState('domcontentloaded');
          await studentPage.pos.flashCardPage.page.waitForTimeout(500);
        });

        await test.step(`Attempt the flashcard with Still learning`, async () => {
          await slowExpect(studentPage.pos.flashCardPage.subjectText('Chemistry'), "Verify chemistry subject is visible").toBeVisible();
          await studentPage.pos.flashCardPage.subjectText('Chemistry').click();
          await studentPage.pos.flashCardPage.checkAndClickTopicUnderSelectTopic('Atomic Structure');
          await studentPage.pos.flashCardPage.clickOnStartButton(studentPage.pos.flashCardPage.url);
          await studentPage.pos.flashCardPage.getCardCount('stillLearning');
          await slowExpect(studentPage.pos.flashCardPage.closeFlashCardIcon, "Verify close button is visible").toBeVisible();
          await studentPage.pos.flashCardPage.closeFlashCardIcon.click();
          await slowExpect(studentPage.pos.flashCardPage.closeButton, "Verify close button is visible").toBeVisible();
          await studentPage.pos.flashCardPage.closeButton.click();
          await studentPage.pos.flashCardPage.page.waitForLoadState('networkidle');
        });

        await test.step(`Click on the Improvement book on Quick Actions`, async () => {
          await slowExpect(studentPage.pos.homePage.improvementBookButton, "Verify improvement book title is visible").toBeVisible();
          await studentPage.pos.homePage.improvementBookButton.click();
          await studentPage.pos.revisionNotesPage.page.waitForLoadState('networkidle');
          await studentPage.pos.flashCardPage.page.waitForTimeout(500);
        });

        await test.step(`Verify the flashcard under Improvement book page`, async () => {
          await slowExpect(studentPage.pos.revisionNotesPage.selectTopic('Atomic Structure'), "Verify atomic structure topic is visible").toBeVisible();
          await slowExpect(studentPage.pos.homePage.atomicStructureButton, "Verify atomic structure button is visible").toBeVisible();
          await studentPage.pos.homePage.atomicStructureButton.click();
          count= await studentPage.pos.flashCardPage.flashcardsToBeReviewedCount.textContent() ?? "0";
          await slowExpect(studentPage.pos.homePage.flashcardsToBeReviewed, "Verify flashcards to be reviewed is visible").toBeVisible();
          await slowExpect(studentPage.pos.homePage.viewButton, "Verify view button is visible").toBeVisible();
          await studentPage.pos.homePage.viewButton.click();
          await slowExpect(studentPage.pos.flashCardPage.stillLearningText, "Verify still learning text is visible").toBeVisible();
        });

        await test.step(`Navigate to home page`, async () => {
          await studentPage.goto(studentPage.pos.homePage.url);
          await studentPage.waitForLoadState('networkidle');
        });

        await test.step(`Click on the Flashcard on Continue revising Flashcards widget`, async () => {
          await slowExpect(studentPage.pos.homePage.continueRevisingFlashCardsTitle, "Verify continue revising flashcards text is visible").toBeVisible();
          await slowExpect(studentPage.pos.homePage.continueRevisingFlashCardsTitle1, "Verify continue revising flashcards text is visible").toBeVisible();
          await studentPage.pos.homePage.continueRevisingFlashCardsTitle1.click();
          await slowExpect(studentPage.pos.homePage.unSeenCardsSelect, "Verify unseen cards select is visible").toBeVisible();
          await studentPage.pos.homePage.unSeenCardsSelect.click();
          await slowExpect(studentPage.pos.homePage.startRevisionButton, "Verify start revision button is visible").toBeVisible();
          await studentPage.pos.homePage.startRevisionButton.click();
          await slowExpect(studentPage.pos.flashCardPage.stillLearningButton, "Verify still learning text is visible").toBeVisible();
          await studentPage.pos.flashCardPage.stillLearningButton.click();
           await slowExpect(studentPage.pos.flashCardPage.closeButton, "Verify close button is visible").toBeVisible();
           await studentPage.pos.flashCardPage.closeButton.click();
        });

        await test.step(`Verify that the flashcard count is increased`, async () => {
           await slowExpect(studentPage.pos.homePage.improvementBookButton, "Verify improvement book title is visible").toBeVisible();
            await studentPage.pos.homePage.improvementBookButton.click();
            await studentPage.pos.revisionNotesPage.page.waitForLoadState('domcontentloaded');
            await studentPage.pos.revisionNotesPage.page.waitForTimeout(500);
            await slowExpect(studentPage.pos.revisionNotesPage.selectTopic('Atomic Structure'), "Verify atomic structure topic is visible").toBeVisible();
            await slowExpect(studentPage.pos.homePage.atomicStructureButton, "Verify atomic structure button is visible").toBeVisible();
            await studentPage.pos.homePage.atomicStructureButton.click();
          const count1 = await studentPage.pos.flashCardPage.flashcardsToBeReviewedCount.textContent() ?? "0";
          console.log("Counts:",count1);
          if(parseInt(count1) > parseInt(count)){
            console.log("Flashcard count is increased");
          }
          else{
            throw new Error("Flashcard count is not increased");
          }
        });


      });

      test('Ensure that Flashcard count is decreased when attempt the Flashcards on Improvement book page', async ({ studentPage }) => {
        await test.step(`Login to the student side`, async () => {
          await studentPage.login();
          await slowExpect(studentPage.pos.homePage.exploreMenu, 'Verify "explore side menu" text is visible').toBeVisible();
        });
        await test.step(`Verify the flashcard under Quick Actions widget`, async () => {
        await slowExpect(studentPage.pos.homePage.quickActionsTitleText, "Verify quick actions title is visible").toBeVisible();
          await slowExpect(studentPage.pos.homePage.flashCardButton, "Verify flashcard title is visible").toBeVisible();
          await studentPage.pos.homePage.flashCardButton.click();
          await studentPage.pos.flashCardPage.page.waitForLoadState('networkidle');
          await studentPage.pos.flashCardPage.page.waitForTimeout(500);
        });

        await test.step(`Attempt the flashcard with Still learning`, async () => {
          await slowExpect(studentPage.pos.flashCardPage.subjectText('Chemistry'), "Verify chemistry subject is visible").toBeVisible();
          await studentPage.pos.flashCardPage.subjectText('Chemistry').click();
          await studentPage.pos.flashCardPage.checkAndClickTopicUnderSelectTopic('Atomic Structure');
          await studentPage.pos.flashCardPage.clickOnStartButton(studentPage.pos.flashCardPage.url);
          await studentPage.pos.flashCardPage.getCardCount('stillLearning');
          await slowExpect(studentPage.pos.flashCardPage.closeFlashCardIcon, "Verify close button is visible").toBeVisible();
          await studentPage.pos.flashCardPage.closeFlashCardIcon.click();
          await slowExpect(studentPage.pos.flashCardPage.closeButton, "Verify close button is visible").toBeVisible();
          await studentPage.pos.flashCardPage.closeButton.click();
          await studentPage.pos.flashCardPage.page.waitForLoadState('networkidle');
        });

        await test.step(`Click on the Improvement book on Quick Actions`, async () => {
          await slowExpect(studentPage.pos.homePage.improvementBookButton, "Verify improvement book title is visible").toBeVisible();
          await studentPage.pos.homePage.improvementBookButton.click();
          await studentPage.pos.revisionNotesPage.page.waitForLoadState('networkidle');
          await studentPage.pos.flashCardPage.page.waitForTimeout(500);
        });

        await test.step(`Verify the flashcard under Improvement book page`, async () => {
          await slowExpect(studentPage.pos.revisionNotesPage.selectTopic('Atomic Structure'), "Verify atomic structure topic is visible").toBeVisible();
          await slowExpect(studentPage.pos.homePage.atomicStructureButton, "Verify atomic structure button is visible").toBeVisible();
          await studentPage.pos.homePage.atomicStructureButton.click();
          const count = await studentPage.pos.flashCardPage.flashcardsToBeReviewedCount.textContent() ?? "0";
          await slowExpect(studentPage.pos.homePage.flashcardsToBeReviewed, "Verify flashcards to be reviewed is visible").toBeVisible();
          await slowExpect(studentPage.pos.homePage.viewButton, "Verify view button is visible").toBeVisible();
          await studentPage.pos.homePage.viewButton.click();
          await slowExpect(studentPage.pos.flashCardPage.stillLearningText, "Verify still learning text is visible").toBeVisible();
          await slowExpect(studentPage.pos.homePage.reviseCard, "Verify revise card is visible").toBeVisible();
          await studentPage.pos.homePage.reviseCard.click();
          await studentPage.pos.flashCardPage.getCardCount('iKnowThis');
          await slowExpect(studentPage.pos.flashCardPage.closeFlashCardIcon, "Verify close button is visible").toBeVisible();
          await studentPage.pos.flashCardPage.closeFlashCardIcon.click();
          await slowExpect(studentPage.pos.flashCardPage.closeButton, "Verify close button is visible").toBeVisible();
          await studentPage.pos.flashCardPage.closeButton.click();
          await studentPage.pos.flashCardPage.page.waitForLoadState('networkidle');
          const count1 = await studentPage.pos.flashCardPage.flashcardsToBeReviewedCount.textContent() ?? "0";
          console.log("Counts:",count, count1);
          if(parseInt(count) > parseInt(count1)){
            console.log("Flashcard count is decreased");
          }
          else{
            throw new Error("Flashcard count is not decreased");
          }
        });
        
      });


    }
})
